import { DashboardPage } from "@/modules/dashboard/pages/dashboard";
import { Outlet, useLocation } from "@tanstack/react-router";
import { useEffect } from "react";
import { Header } from "../../header";
import { SidebarMain } from "../../siderbar/desktop/index.component";
import MobileLayout from "../mobile/mobile-layout";

const MAIN_CONTENT_ID = "main-scrollable-area";

export const DashboardLayout = () => {
	const location = useLocation();

	useEffect(() => {
		const mainContent = document.getElementById(MAIN_CONTENT_ID);
		mainContent?.scrollTo({
			top: 0,
			behavior: "instant",
		});
	}, [location.pathname]);

	const isDashboardRoute = location.pathname === "/";

	return (
		<main className="flex w-full max-h-screen min-h-screen bg-transparent">
			<div className="hidden sm:flex w-full">
				<SidebarMain />
				<div id={MAIN_CONTENT_ID} className="flex relative flex-col flex-1 p-2 overflow-y-auto min-h-0 text-black">
					<Header />
					<div className="flex-1 h-full flex bg-transparent rounded-lg items-start justify-center w-full">
						{isDashboardRoute ? <DashboardPage /> : <Outlet />}
					</div>
				</div>
			</div>
			<div className="flex sm:hidden w-full">
				<MobileLayout />
			</div>
		</main>
	);
};
