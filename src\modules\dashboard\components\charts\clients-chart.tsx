import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>dingUp, <PERSON>ader2, Users } from "lucide-react";
import { CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis, Area, AreaChart } from "recharts";
import { IChartData } from "../../types/dashboard.types";
import { useResponsiveCharts } from "../../hooks/use-responsive-charts";
import React from "react";

interface IClientsChartProps {
	data: IChartData[];
	isLoading?: boolean;
}

interface ICustomTooltipProps {
	active?: boolean;
	payload?: Array<{
		value: number;
		dataKey: string;
		color: string;
	}>;
	label?: string;
}

const CustomTooltip = ({ active, payload, label }: ICustomTooltipProps) => {
	if (active && payload && payload.length) {
		const value = payload[0].value;

		return (
			<div className="bg-white/95 backdrop-blur-sm p-4 border border-gray-200/50 rounded-xl shadow-xl">
				<p className="text-sm font-semibold text-gray-900 mb-2">{label}</p>
				<div className="flex items-center gap-2">
					<div className="w-3 h-3 rounded-full bg-gradient-to-r from-emerald-500 to-emerald-600"></div>
					<p className="text-sm font-medium text-emerald-600">
						{value} {value === 1 ? "cliente" : "clientes"}
					</p>
				</div>
			</div>
		);
	}
	return null;
};

const EmptyState = () => (
	<div className="flex flex-col items-center justify-center h-full text-gray-500">
		<Users size={48} className="mb-3 opacity-50" />
		<p className="text-sm font-medium">Nenhum cliente encontrado</p>
		<p className="text-xs text-gray-400">Dados de clientes aparecerão aqui</p>
	</div>
);

const LoadingState = () => (
	<div className="flex flex-col items-center justify-center h-full text-gray-500">
		<Loader2 size={32} className="animate-spin mb-3 text-emerald-500" />
		<p className="text-sm font-medium">Carregando clientes...</p>
	</div>
);

export const ClientsChart = React.memo(({ data, isLoading = false }: IClientsChartProps) => {
	const { isMobile, chartHeight, margins, fontSize } = useResponsiveCharts();
	const hasData = data && data.length > 0;

	return (
		<div className="bg-gradient-to-br from-white to-gray-50/50 p-4 sm:p-6 rounded-2xl shadow-sm border border-gray-100/50 hover:shadow-md transition-all duration-300">
			<div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
				<div className="flex items-center gap-3">
					<div className="p-2.5 bg-gradient-to-br from-emerald-100 to-emerald-50 rounded-xl shadow-sm">
						<MapPin size={20} className="text-emerald-600" />
					</div>
					<div>
						<h3 className="font-semibold text-gray-900 text-base sm:text-lg">Clientes por Região</h3>
						<p className="text-xs sm:text-sm text-gray-500">Distribuição geográfica dos clientes</p>
					</div>
				</div>
				<button className="flex items-center gap-2 text-xs sm:text-sm text-blue-600 hover:text-blue-700 transition-colors self-start sm:self-auto px-3 py-1.5 rounded-lg hover:bg-blue-50">
					<TrendingUp size={14} />
					<span className="hidden sm:inline">Ver detalhes</span>
					<span className="sm:hidden">Detalhes</span>
				</button>
			</div>

			<div className="w-full transition-all duration-300" style={{ height: chartHeight }}>
				{isLoading ? (
					<LoadingState />
				) : !hasData ? (
					<EmptyState />
				) : (
					<ResponsiveContainer width="100%" height="100%">
						<AreaChart data={data} margin={margins}>
							<defs>
								<linearGradient id="areaGradient" x1="0" y1="0" x2="0" y2="1">
									<stop offset="0%" stopColor="#10b981" stopOpacity={0.3} />
									<stop offset="100%" stopColor="#10b981" stopOpacity={0.05} />
								</linearGradient>
								<linearGradient id="lineGradient" x1="0" y1="0" x2="1" y2="0">
									<stop offset="0%" stopColor="#059669" />
									<stop offset="50%" stopColor="#10b981" />
									<stop offset="100%" stopColor="#34d399" />
								</linearGradient>
							</defs>
							<CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" strokeOpacity={0.6} vertical={false} />
							<XAxis
								dataKey="name"
								axisLine={false}
								tickLine={false}
								tick={{
									fontSize: fontSize.tick,
									fill: "#6b7280",
									fontWeight: 500,
								}}
								interval={1}
							/>
							<YAxis
								axisLine={false}
								tickLine={false}
								tick={{
									fontSize: fontSize.tick,
									fill: "#6b7280",
									fontWeight: 500,
								}}
								width={isMobile ? 40 : 60}
								tickFormatter={value => {
									if (isMobile) {
										return value.toString();
									}
									return `${value}`;
								}}
							/>
							<Tooltip content={<CustomTooltip />} />
							<Area
								type="monotone"
								dataKey="valor"
								stroke="url(#lineGradient)"
								fill="url(#areaGradient)"
								strokeWidth={3}
								dot={{
									fill: "#10b981",
									strokeWidth: 2,
									r: isMobile ? 4 : 6,
									className: "drop-shadow-sm",
								}}
								activeDot={{
									r: isMobile ? 6 : 8,
									stroke: "#10b981",
									strokeWidth: 2,
									fill: "#ffffff",
									className: "drop-shadow-md",
								}}
								className="drop-shadow-sm"
							/>
						</AreaChart>
					</ResponsiveContainer>
				)}
			</div>
		</div>
	);
});

ClientsChart.displayName = "ClientsChart";
