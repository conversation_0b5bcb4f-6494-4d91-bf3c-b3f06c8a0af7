import { MainSkeletonContainer } from "@/shared/components/container/main-skeleton-container";
import { useParams } from "@tanstack/react-router";
import axios from "axios";
import { motion } from "framer-motion";
import { Home, Pencil } from "lucide-react";
import { useEffect, useState } from "react";
import { ProductForm } from "../components/product-form/product-form";
import { PRODUCT_CONFIG } from "../data/product-config";
import { useFindProductById } from "../hooks/product/find-product-by-id.hook";
import { useUpdateProduct } from "../hooks/product/update-product.hook";
import { useProductForm } from "../hooks/use-product-form.hook";

const fetchImageFile = async (url: string): Promise<File> => {
	try {
		const controller = new AbortController();
		setTimeout(() => controller.abort(), 15000);

		const urlDev = url.replace("localhost:7000", import.meta.env.VITE_API_IP || "************:7000");
		const response = await axios.get(urlDev, {
			responseType: "blob",
			signal: controller.signal,
		});
		if (!response.data || !(response.data instanceof Blob) || response.data.size === 0) throw new Error("Falha ao carregar imagem");
		const contentType = response.headers["content-type"];
		if (!contentType?.startsWith("image/")) throw new Error("Tipo inválido");
		const fileName = url.split("/").pop() || "product-image.jpg";
		return new File([response.data as Blob], fileName, { type: contentType });
	} catch {
		throw new Error("Falha ao carregar imagem do produto");
	}
};

const EditProductPage = () => {
	const params = useParams({ from: "/produtos/$product-id" });
	const productId = Number(params["product-id"]);
	const { data, isLoading } = useFindProductById(productId);
	const { updateProductMutation } = useUpdateProduct();
	const [isLoadingImage, setIsLoadingImage] = useState(false);
	const [imageError, setImageError] = useState<string | null>(null);

	const {
		methods,
		handleSubmit: onSubmit,
		isSubmitting,
	} = useProductForm({
		onSubmit: async formData => {
			await updateProductMutation.mutateAsync({
				productId,
				items: {
					barcode: formData.barcode ?? "",
					ncm: formData.ncm ?? "",
					costPrice: formData.costPrice ?? 0,
					price: formData.price ?? 0,
					name: formData.name ?? "",
					description: formData.description ?? "",
					status: true,
					supplierId: typeof formData.supplierId === "string" ? Number(formData.supplierId) : formData.supplierId,
					categoryId: typeof formData.categoryId === "string" ? Number(formData.categoryId) : formData.categoryId,
				},
			});
		},
	});

	useEffect(() => {
		if (data?.success && data.data) {
			const product = data.data;
			methods.reset({
				...product,
				images: [],
				categoryId: product.category.id?.toString(),
				supplierId: product.supplier?.id?.toString() ?? "",
				code: product.code ?? "",
				name: product.name ?? "",

				description: product.description ?? "",
				costPrice: product.costPrice ?? 0,
				price: product.price ?? 0,
			});

			if (product.imageUrl) {
				setIsLoadingImage(true);
				setImageError(null);
				const timeout = setTimeout(() => {
					setIsLoadingImage(false);
					setImageError("Tempo excedido ao carregar imagem");
				}, 3000);
				fetchImageFile(product.imageUrl)
					.then(imageFile => {
						clearTimeout(timeout);
						methods.setValue("images", [imageFile]);
						setIsLoadingImage(false);
					})
					.catch(() => {
						clearTimeout(timeout);
						setIsLoadingImage(false);
						setImageError(product.imageUrl);
					});
			}
		}
	}, [data, methods]);

	if (isLoading)
		return (
			<div className="flex flex-col items-center justify-center p-8">
				<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} className="flex flex-col items-center gap-4">
					<div className="h-8 w-8 animate-spin rounded-full border-4 border-mainColor border-t-transparent" />
					<p className="text-gray-600">Carregando produto...</p>
				</motion.div>
			</div>
		);

	if (!data?.success)
		return (
			<div className="flex flex-col items-center justify-center p-8">
				<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="text-center">
					<p className="text-red-600 text-xl mb-2">Ops! Algo deu errado.</p>
					<p className="text-gray-600">{data?.data.message}</p>
				</motion.div>
			</div>
		);

	return (
		<MainSkeletonContainer
			pageTitle="Editar produto"
			iconTitle={Pencil}
			itemsBreadcrumb={[
				{ href: "/", label: "Página inicial", icon: Home },
				{ href: "/produtos", label: "Produtos", icon: PRODUCT_CONFIG.Icon },
			]}
			currentBreadcrumb={{
				href: `/produtos/${productId}`,
				label: "Editar produto",
				icon: Pencil,
			}}
		>
			<ProductForm
				isInEditMode
				methods={methods}
				onSubmit={onSubmit}
				isSubmitting={isSubmitting}
				isLoadingImage={isLoadingImage}
				imageError={imageError}
			/>
		</MainSkeletonContainer>
	);
};

export default EditProductPage;
