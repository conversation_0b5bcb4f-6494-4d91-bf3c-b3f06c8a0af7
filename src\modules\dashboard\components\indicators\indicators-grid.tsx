import { TrendingUp, TrendingDown, DollarSign, Users, Package, Target } from "lucide-react";
import { motion } from "framer-motion";
import { IDashboardIndicator } from "../../types/dashboard.types";
import React from "react";

interface IIndicatorsGridProps {
	indicators: IDashboardIndicator[];
}

const getIndicatorIcon = (titulo: string) => {
	switch (titulo.toLowerCase()) {
		case "faturamento":
			return DollarSign;
		case "novos clientes":
			return Users;
		case "produtos vendidos":
			return Package;
		case "tickets médio":
		case "ticket médio":
			return Target;
		default:
			return TrendingUp;
	}
};

const IndicatorCard = React.memo(({ indicator }: { indicator: IDashboardIndicator }) => {
	const IconComponent = getIndicatorIcon(indicator.titulo);
	const isPositive = indicator.positivo;

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
			className="bg-white p-6 rounded-[15px] shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200"
		>
			<div className="flex items-start justify-between">
				<div className="flex-1">
					<div className="flex items-center gap-3 mb-3">
						<div className={`p-2 rounded-lg ${isPositive ? "bg-green-100" : "bg-red-100"}`}>
							<IconComponent size={20} className={isPositive ? "text-green-600" : "text-red-600"} />
						</div>
						<h3 className="text-sm font-medium text-gray-600">{indicator.titulo}</h3>
					</div>

					<div className="space-y-2">
						<p className="text-2xl font-bold text-gray-900">{indicator.valor}</p>
						<div className="flex items-center gap-1">
							{isPositive ? <TrendingUp size={16} className="text-green-600" /> : <TrendingDown size={16} className="text-red-600" />}
							<span className={`text-sm font-medium ${isPositive ? "text-green-600" : "text-red-600"}`}>{indicator.percentual}</span>
							<span className="text-xs text-gray-500 ml-1">em relação ao período anterior</span>
						</div>
					</div>
				</div>
			</div>
		</motion.div>
	);
});

IndicatorCard.displayName = "IndicatorCard";

export const IndicatorsGrid = React.memo(({ indicators }: IIndicatorsGridProps) => {
	return (
		<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
			{indicators.map((indicator, index) => (
				<motion.div
					key={indicator.id}
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.3, delay: index * 0.1 }}
				>
					<IndicatorCard indicator={indicator} />
				</motion.div>
			))}
		</div>
	);
});

IndicatorsGrid.displayName = "IndicatorsGrid";
