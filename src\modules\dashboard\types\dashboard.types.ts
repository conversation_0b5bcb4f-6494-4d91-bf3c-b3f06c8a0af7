export interface IDashboardIndicator {
	id: number;
	titulo: string;
	valor: string;
	percentual: string;
	positivo: boolean;
	icon?: React.ElementType;
}

export interface IChartData {
	name: string;
	valor?: number;
	value?: number;
}

export interface IDashboardReport {
	id: number;
	nome: string;
	descricao: string;
}

export interface IDashboardChartsData {
	vendasMensais: IChartData[];
	produtosMaisVendidos: IChartData[];
	clientesPorRegiao: IChartData[];
}

export interface IDashboardData {
	indicators: IDashboardIndicator[];
	charts: IDashboardChartsData;
	reports: IDashboardReport[];
}

export interface IDashboardFilters {
	periodo: string;
	categoria: string;
	dataInicio?: string;
	dataFim?: string;
}

export type PeriodType = "diario" | "semanal" | "mensal" | "anual";
