import { useState } from "react";
import { useForm, UseFormReturn } from "react-hook-form";
import { IDashboardFilters, PeriodType, IDashboardData } from "../types/dashboard.types";

// Mock data - em um cenário real, estes dados viriam de APIs
const mockDashboardData: IDashboardData = {
	indicators: [
		{ id: 1, titulo: "Faturamento", valor: "R$ 152.350,00", percentual: "+15%", positivo: true },
		{ id: 2, titulo: "Novos Clientes", valor: "48", percentual: "+22%", positivo: true },
		{ id: 3, titulo: "Produtos Vendidos", valor: "1.243", percentual: "+5%", positivo: true },
		{ id: 4, titulo: "Tickets Médio", valor: "R$ 89,50", percentual: "-3%", positivo: false },
	],
	charts: {
		vendasMensais: [
			{ name: "<PERSON>", valor: 4000 },
			{ name: "<PERSON><PERSON>", valor: 3000 },
			{ name: "<PERSON>", valor: 5000 },
			{ name: "Abr", valor: 2780 },
			{ name: "<PERSON>", valor: 6890 },
		],
		produtosMaisVendidos: [
			{ name: "Detergente", value: 400 },
			{ name: "Desinfetante", value: 300 },
			{ name: "Sabão em pó", value: 250 },
			{ name: "Alvejante", value: 200 },
			{ name: "Amaciante", value: 150 },
		],
		clientesPorRegiao: [
			{ name: "Vila Flores", valor: 24 },
			{ name: "Jardim Primavera", valor: 40 },
			{ name: "São José", valor: 15 },
			{ name: "Bela Vista", valor: 18 },
			{ name: "Monte Verde", valor: 10 },
		],
	},
	reports: [
		{ id: 1, nome: "Vendas Mensais", descricao: "Relatório detalhado de vendas por mês" },
		{ id: 2, nome: "Catálogo de produtos", descricao: "Listagem completa de produtos disponíveis com especificações" },
		{ id: 3, nome: "Estoque Atual", descricao: "Situação atual do estoque com alertas" },
		{ id: 4, nome: "Clientes por Bairro", descricao: "Distribuição geográfica dos clientes" },
		{ id: 5, nome: "Produtos mais Vendidos", descricao: "Produtos com maior saída" },
	],
};

interface IUseDashboardDataReturn {
	methods: UseFormReturn<IDashboardFilters>;
	selectedPeriod: PeriodType;
	activeReport: number | null;
	handlePeriodChange: (period: PeriodType) => void;
	handleOpenReport: (reportId: number) => void;
	handleCloseReport: () => void;
	handleGeneratePDF: (reportId: number) => void;
	dashboardData: IDashboardData;
}

export const useDashboardData = (): IUseDashboardDataReturn => {
	const [selectedPeriod, setSelectedPeriod] = useState<PeriodType>("mensal");
	const [activeReport, setActiveReport] = useState<number | null>(null);

	const methods = useForm<IDashboardFilters>({
		defaultValues: {
			periodo: "ultimo-mes",
			categoria: "todos",
			dataInicio: "",
			dataFim: "",
		},
	});

	const handlePeriodChange = (period: PeriodType) => {
		setSelectedPeriod(period);
	};

	const handleOpenReport = (reportId: number) => {
		setActiveReport(reportId);
	};

	const handleCloseReport = () => {
		setActiveReport(null);
	};

	const handleGeneratePDF = (reportId: number) => {
		const report = mockDashboardData.reports.find(r => r.id === reportId);
		if (report) {
			alert(`Gerando PDF do relatório: ${report.nome}`);
		} else {
			alert("Relatório não encontrado");
		}
	};

	return {
		methods,
		selectedPeriod,
		activeReport,
		handlePeriodChange,
		handleOpenReport,
		handleCloseReport,
		handleGeneratePDF,
		dashboardData: mockDashboardData,
	};
};
