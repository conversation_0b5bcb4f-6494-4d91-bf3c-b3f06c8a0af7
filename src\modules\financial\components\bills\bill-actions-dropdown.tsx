import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/shared/components/ui/dropdown-menu";
import { CustomTooltip } from "@/shared/components/custom/tooltip";
import { MoreVertical, Pencil, Trash2 } from "lucide-react";
import { useState } from "react";
import { IBillDto } from "../../dtos/bills/find-all.dto";
import { useDeleteBill } from "../../hooks/bills/delete.hook";
import { DeleteBillModal } from "./delete-bill-modal";
import { EditBillModal } from "./edit-bill-modal";

interface BillActionsDropdownProps {
	bill: IBillDto;
}

export const BillActionsDropdown = ({ bill }: BillActionsDropdownProps) => {
	const [isEditModalOpen, setIsEditModalOpen] = useState(false);
	const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
	const deleteBill = useDeleteBill();

	// Verifica se a conta está paga (tem data de pagamento)
	const isPaid = Boolean(bill.paymentDate && bill.paymentDate.trim() !== "");

	const handleDelete = async () => {
		try {
			await deleteBill.mutateAsync({ id: bill.id });
			setIsDeleteModalOpen(false);
		} catch (error) {
			console.error("Erro ao excluir conta:", error);
		}
	};

	const handleEditClick = () => {
		if (!isPaid) {
			setIsEditModalOpen(true);
		}
	};

	const handleDeleteClick = () => {
		if (!isPaid) {
			setIsDeleteModalOpen(true);
		}
	};

	return (
		<>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
						<MoreVertical size={18} className="text-gray-500" />
					</button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end" className="w-[160px] bg-white">
					<CustomTooltip content={isPaid ? "Não é possível editar uma conta que já foi paga" : ""}>
						<DropdownMenuItem
							onClick={handleEditClick}
							className={`cursor-pointer ${isPaid ? "opacity-50 cursor-not-allowed" : ""}`}
							disabled={isPaid}
						>
							<Pencil size={16} className="mr-2" />
							Editar
						</DropdownMenuItem>
					</CustomTooltip>

					<CustomTooltip content={isPaid ? "Não é possível excluir uma conta que já foi paga" : ""}>
						<DropdownMenuItem
							onClick={handleDeleteClick}
							className={`cursor-pointer text-red-600 ${isPaid ? "opacity-50 cursor-not-allowed" : ""}`}
							disabled={isPaid}
						>
							<Trash2 size={16} className="mr-2" />
							Excluir
						</DropdownMenuItem>
					</CustomTooltip>
				</DropdownMenuContent>
			</DropdownMenu>

			<EditBillModal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} billId={bill.id} />
			<DeleteBillModal
				isOpen={isDeleteModalOpen}
				onClose={() => setIsDeleteModalOpen(false)}
				onConfirm={handleDelete}
				isLoading={deleteBill.isPending}
			/>
		</>
	);
};
