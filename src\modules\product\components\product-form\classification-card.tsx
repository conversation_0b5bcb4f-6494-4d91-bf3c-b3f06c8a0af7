import { Info, Tag, Users } from "lucide-react";
import { useState } from "react";
import { Controller, UseFormReturn } from "react-hook-form";
import { ProductFormData } from "../../validators/product/product-form.schema";
import { CategorySelect } from "../category/category-select";
import { GroupSelect } from "../group/group-select";
import { InputField } from "./input-field";

interface ClassificationCardProps {
	methods: UseFormReturn<ProductFormData>;
	isInEditMode?: boolean;
}

export const ClassificationCard: React.FC<ClassificationCardProps> = ({ methods }): JSX.Element => {
	const [selectedGroupId, setSelectedGroupId] = useState<string | null>(null);

	const handleGroupChange = (groupId: string | null): void => {
		setSelectedGroupId(groupId);
		methods.setValue("categoryId", "");
	};

	const handleCategoryChange = (newCategoryId: string | null): void => {
		methods.setValue("categoryId", newCategoryId ?? "");
	};

	return (
		<div className="bg-gray-50/50 rounded-lg p-4 border border-gray-100">
			<div className="flex items-center gap-2 mb-3">
				<div className="p-1.5 bg-mainColor/10 rounded-lg">
					<Tag size={16} className="text-mainColor" />
				</div>
				<label className="text-sm font-medium text-gray-700">Classificação</label>
			</div>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<InputField label="NCM" name="ncm" required placeholder="Ex: 8471.30.19" icon={<Info size={14} />} methods={methods} />
				<GroupSelect label="Grupo" icon={<Users size={14} />} value={selectedGroupId || undefined} onChange={handleGroupChange} />
				<div className="md:col-span-2">
					<Controller
						name="categoryId"
						control={methods.control}
						render={({ field, fieldState }) => (
							<CategorySelect
								label="Categoria"
								required
								icon={<Tag size={14} />}
								value={field.value || null}
								onChange={handleCategoryChange}
								error={fieldState.error?.message as string}
								groupId={selectedGroupId}
							/>
						)}
					/>
				</div>
			</div>
		</div>
	);
};
