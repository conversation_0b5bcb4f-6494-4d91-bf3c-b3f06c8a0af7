import { MainSkeletonContainer } from "@/shared/components/container/main-skeleton-container";
import { Home } from "lucide-react";
import { DASHBOARD_CONFIG } from "../data/dashboard-config";
import { DashboardFiltersSection } from "../components/filters";
import { IndicatorsGrid } from "../components/indicators/indicators-grid";

import { ReportsSection } from "../components/reports/reports-section";
import { useDashboardData } from "../hooks/use-dashboard-data";
import { ChartsSection } from "../components";

export function DashboardPage() {
	const { methods, selectedPeriod, activeReport, handlePeriodChange, handleOpenReport, handleCloseReport, handleGeneratePDF, dashboardData } =
		useDashboardData();

	return (
		<MainSkeletonContainer
			iconTitle={DASHBOARD_CONFIG.Icon}
			itemsBreadcrumb={[{ href: "/", label: "Página inicial", icon: Home }]}
			currentBreadcrumb={{
				href: DASHBOARD_CONFIG.path,
				label: "Dashboard",
				icon: DASHBOARD_CONFIG.Icon,
			}}
			pageTitle="Dashboard"
		>
			<DashboardFiltersSection methods={methods} />
			<IndicatorsGrid indicators={dashboardData.indicators} />
			<ChartsSection selectedPeriod={selectedPeriod} onPeriodChange={handlePeriodChange} chartsData={dashboardData.charts} />
			<ReportsSection
				reports={dashboardData.reports}
				activeReport={activeReport}
				onOpenReport={handleOpenReport}
				onCloseReport={handleCloseReport}
				onGeneratePDF={handleGeneratePDF}
			/>
		</MainSkeletonContainer>
	);
}
