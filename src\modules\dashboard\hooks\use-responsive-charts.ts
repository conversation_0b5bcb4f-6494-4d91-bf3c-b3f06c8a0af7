import { useState, useEffect, useMemo } from "react";

interface IUseResponsiveChartsReturn {
	isMobile: boolean;
	isTablet: boolean;
	isDesktop: boolean;
	chartHeight: number;
	pieRadius: number;
	barMaxSize: number;
	margins: {
		top: number;
		right: number;
		left: number;
		bottom: number;
	};
	fontSize: {
		tick: number;
		label: number;
	};
}

export const useResponsiveCharts = (): IUseResponsiveChartsReturn => {
	const [windowWidth, setWindowWidth] = useState<number>(
		typeof window !== "undefined" ? window.innerWidth : 1024
	);

	useEffect(() => {
		if (typeof window === "undefined") return;

		const handleResize = () => {
			setWindowWidth(window.innerWidth);
		};

		window.addEventListener("resize", handleResize);
		return () => window.removeEventListener("resize", handleResize);
	}, []);

	const breakpoints = useMemo(() => ({
		isMobile: windowWidth < 768,
		isTablet: windowWidth >= 768 && windowWidth < 1024,
		isDesktop: windowWidth >= 1024,
	}), [windowWidth]);

	const chartHeight = useMemo(() => {
		if (breakpoints.isMobile) return 250;
		if (breakpoints.isTablet) return 300;
		return 320;
	}, [breakpoints]);

	const pieRadius = useMemo(() => {
		if (breakpoints.isMobile) return 60;
		if (breakpoints.isTablet) return 80;
		return 90;
	}, [breakpoints]);

	const barMaxSize = useMemo(() => {
		if (breakpoints.isMobile) return 40;
		if (breakpoints.isTablet) return 50;
		return 60;
	}, [breakpoints]);

	const margins = useMemo(() => ({
		top: 20,
		right: breakpoints.isMobile ? 10 : 30,
		left: breakpoints.isMobile ? 10 : 20,
		bottom: 5,
	}), [breakpoints]);

	const fontSize = useMemo(() => ({
		tick: breakpoints.isMobile ? 10 : 12,
		label: breakpoints.isMobile ? 12 : 14,
	}), [breakpoints]);

	return {
		...breakpoints,
		chartHeight,
		pieRadius,
		barMaxSize,
		margins,
		fontSize,
	};
};
