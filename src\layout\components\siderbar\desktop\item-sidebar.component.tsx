import { sidebarExpandAtom } from "@/layout/states/sidebar-expand.state";
import { ISubItemSidebar } from "@/layout/types/sidebar-active-item.type";
import { shouldNavigateDirectly } from "@/shared/routes/module-routes.factory";
import * as AccessibleIcon from "@radix-ui/react-accessible-icon";
import { useRouter } from "@tanstack/react-router";
import { motion } from "framer-motion";
import { useAtomValue } from "jotai";
import { ChevronDown, ChevronRight } from "lucide-react";
import React, { useEffect } from "react";

interface ItemMenuProps {
	Icon: React.ComponentType<{ className?: string }>;
	accessibleDescription: string;
	isActiveItem: boolean;
	handleActiveItem: ({ subItem }: { subItem?: ISubItemSidebar }) => void;
	itemLabel: string;
	subItems?: ISubItemSidebar[];
	isOpen: boolean;
	onToggle: () => void;
	activePath: string;
	setActivePath: React.Dispatch<React.SetStateAction<string>>;
}

export const ItemMenu: React.FC<ItemMenuProps> = React.memo(
	({ Icon, accessibleDescription, isActiveItem, handleActiveItem, itemLabel, subItems, isOpen, onToggle, activePath, setActivePath }) => {
		const expandStatus = useAtomValue(sidebarExpandAtom);
		const hasSubItems = !!subItems?.length;
		const shouldNavigateDirect = shouldNavigateDirectly(subItems);
		const router = useRouter();

		const onClickItem = () => {
			if (hasSubItems && !shouldNavigateDirect) {
				onToggle();
			} else {
				const targetSubItem = subItems?.length === 1 ? subItems[0] : undefined;
				handleActiveItem({ subItem: targetSubItem });
			}
		};

		const onClickSubItem = (sub: ISubItemSidebar) => {
			setActivePath(sub.path);
			handleActiveItem({ subItem: sub });
		};

		useEffect(() => {
			setActivePath(router.latestLocation.pathname);
		}, [router, setActivePath]);

		return (
			<motion.div className={`relative flex flex-col w-full rounded-[15px] ${isOpen ? "bg-gray-50" : ""}`}>
				<motion.button
					aria-label={accessibleDescription}
					onClick={onClickItem}
					className={`flex ${
						isActiveItem ? "bg-gradient-to-r from-mainColor/10 to-indigo-100 shadow-md border border-mainColor/20" : "hover:bg-blue-50/40"
					} ${isOpen ? "rounded-t-[15px]" : "rounded-[15px]"} ${
						expandStatus === "expanded" ? "w-full justify-between gap-2" : "w-[50px] justify-center"
					} h-[50px] items-center transition-all duration-200`}
					whileHover={{ scale: 1.02, y: -1 }}
					whileTap={{ scale: 0.98 }}
				>
					<div className="flex justify-center items-center min-w-[40px] max-w-[40px] h-[40px] overflow-hidden">
						<AccessibleIcon.Root label={accessibleDescription}>
							<Icon
								className={`h-5 w-5 ${
									isActiveItem ? "text-mainColor" : "text-gray-600"
								} hover:text-mainColor cursor-pointer transition-colors duration-200`}
							/>
						</AccessibleIcon.Root>
						{hasSubItems && !shouldNavigateDirect && expandStatus === "collapsed" && (
							<>
								{isOpen ? (
									<ChevronDown className="text-gray-600 w-3.5 h-3.5" />
								) : (
									<ChevronRight className="text-gray-600 w-3.5 h-3.5" />
								)}
							</>
						)}
					</div>

					<motion.div
						animate={{ width: expandStatus === "expanded" ? "130px" : 0 }}
						className="flex overflow-hidden justify-center items-center"
						initial={false}
						exit={{ width: 0 }}
					>
						{expandStatus === "expanded" && (
							<span className={`ml-2 text-sm overflow-hidden flex font-medium ${isActiveItem ? "text-mainColor" : "text-gray-700"}`}>
								{itemLabel}
								{hasSubItems && !shouldNavigateDirect && (
									<>
										{isOpen ? (
											<ChevronDown className="inline-block ml-1 text-gray-500 w-3.5 h-3.5" />
										) : (
											<ChevronRight className="inline-block ml-1 text-gray-500 w-3.5 h-3.5" />
										)}
									</>
								)}
							</span>
						)}
					</motion.div>
				</motion.button>

				{hasSubItems && !shouldNavigateDirect && isOpen && (
					<motion.div
						initial={{ height: 0, opacity: 0 }}
						animate={{ height: "auto", opacity: 1 }}
						transition={{ duration: 0.2 }}
						className={`overflow-hidden flex flex-col border-l-2 border-blue-100 ${expandStatus === "expanded" ? "ml-3" : "ml-1"} rounded-b-[15px] bg-white`}
					>
						{subItems.map(sub => (
							<button
								key={sub.id}
								onClick={() => onClickSubItem(sub)}
								className={`flex items-center gap-2 py-2 px-3 text-xs hover:bg-blue-50/60 transition-all duration-200 ${
									sub.path === activePath ? "bg-mainColor/10 text-mainColor font-medium" : "text-gray-600 font-normal"
								}`}
							>
								<AccessibleIcon.Root label={sub.accessibleDescription}>
									<sub.Icon className={`h-4 w-4 ${sub.path === activePath ? "text-mainColor" : "text-gray-500"}`} />
								</AccessibleIcon.Root>
								{expandStatus === "expanded" && (
									<motion.span
										initial={{ opacity: 0 }}
										animate={{ opacity: 1 }}
										transition={{ duration: 0.2, delay: 0.1 }}
										className="whitespace-nowrap overflow-hidden text-ellipsis"
									>
										{sub.label}
									</motion.span>
								)}
							</button>
						))}
					</motion.div>
				)}
			</motion.div>
		);
	}
);
