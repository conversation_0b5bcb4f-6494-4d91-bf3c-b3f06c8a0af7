import { DashboardPage } from "@/modules/dashboard/pages/dashboard";
import { Outlet, useLocation } from "@tanstack/react-router";
import React, { useRef, useState } from "react";

import HeaderMobile from "../../header/header.mobile";
import { SidebarMobile } from "../../siderbar/mobile";

export default function MobileLayout() {
	const containerRef = useRef<HTMLDivElement>(null);
	const location = useLocation();

	const [isScrolled, setIsScrolled] = useState(false);
	const [isForcedExpanded, setIsForcedExpanded] = useState(false);

	const isDashboardRoute = location.pathname === "/";

	const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
		const scrollTop = e.currentTarget.scrollTop;

		if (scrollTop > 0) {
			setIsScrolled(true);
			setIsForcedExpanded(false);
		} else {
			setIsScrolled(false);
		}
	};

	const isExpanded = !isScrolled || isForcedExpanded;
	const isExpandedDuringScroll = isScrolled && isForcedExpanded;
	return (
		<div ref={containerRef} className="flex min-h-screen w-full flex-col overflow-x-hidden overflow-y-auto" onScroll={handleScroll}>
			<div className="fixed top-0 z-50 left-0 w-full">
				<HeaderMobile
					isExpanded={isExpanded}
					isExpandedDuringScroll={isExpandedDuringScroll}
					onExpandRequest={() => setIsForcedExpanded(true)}
				/>
			</div>

			<div className="w-full pt-24 pb-24 flex-1 max-w-full px-1">{isDashboardRoute ? <DashboardPage /> : <Outlet />}</div>

			<div className="fixed bottom-0 z-50 left-0 w-full">
				<SidebarMobile />
			</div>
		</div>
	);
}
