import { Package, TrendingUp, Loader2 } from "lucide-react";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { IChartData } from "../../types/dashboard.types";
import { useResponsiveCharts } from "../../hooks/use-responsive-charts";
import React, { useMemo } from "react";

interface IProductsChartProps {
	data: IChartData[];
	isLoading?: boolean;
}

const COLORS = [
	"#3b82f6", // Blue
	"#10b981", // Emerald
	"#f59e0b", // Amber
	"#ef4444", // Red
	"#8b5cf6", // Violet
	"#06b6d4", // Cyan
	"#84cc16", // Lime
	"#f97316", // Orange
];

const CustomTooltip = ({ active, payload }: any) => {
	if (active && payload && payload.length) {
		const data = payload[0];
		const total = data.payload.total || 0;
		const percentage = total > 0 ? ((data.value / total) * 100).toFixed(1) : 0;

		return (
			<div className="bg-white/95 backdrop-blur-sm p-4 border border-gray-200/50 rounded-xl shadow-xl">
				<p className="text-sm font-semibold text-gray-900 mb-2">{data.name}</p>
				<div className="space-y-1">
					<div className="flex items-center gap-2">
						<div className="w-3 h-3 rounded-full" style={{ backgroundColor: data.color }} />
						<p className="text-sm font-medium text-gray-700">{data.value} unidades</p>
					</div>
					<p className="text-xs text-gray-500 ml-5">{percentage}% do total</p>
				</div>
			</div>
		);
	}
	return null;
};

const CustomLegend = ({ payload }: any) => {
	return (
		<div className="flex flex-wrap justify-center gap-2 sm:gap-3 mt-4 px-2">
			{payload?.map((entry: any, index: number) => (
				<div key={index} className="flex items-center gap-1.5 min-w-0">
					<div className="w-3 h-3 rounded-full flex-shrink-0 shadow-sm" style={{ backgroundColor: COLORS[index % COLORS.length] }} />
					<span className="text-xs sm:text-sm text-gray-600 truncate font-medium">{entry.value}</span>
				</div>
			))}
		</div>
	);
};

const EmptyState = () => (
	<div className="flex flex-col items-center justify-center h-full text-gray-500">
		<Package size={48} className="mb-3 opacity-50" />
		<p className="text-sm font-medium">Nenhum produto encontrado</p>
		<p className="text-xs text-gray-400">Dados de produtos aparecerão aqui</p>
	</div>
);

const LoadingState = () => (
	<div className="flex flex-col items-center justify-center h-full text-gray-500">
		<Loader2 size={32} className="animate-spin mb-3 text-green-500" />
		<p className="text-sm font-medium">Carregando produtos...</p>
	</div>
);

export const ProductsChart = React.memo(({ data, isLoading = false }: IProductsChartProps) => {
	const { isMobile, chartHeight, pieRadius } = useResponsiveCharts();

	const processedData = useMemo(() => {
		if (!data || data.length === 0) return [];

		const total = data.reduce((sum, item) => sum + (item.value || 0), 0);
		return data.map(item => ({
			...item,
			total,
		}));
	}, [data]);

	const hasData = processedData && processedData.length > 0;

	return (
		<div className="bg-gradient-to-br from-white to-gray-50/50 p-4 sm:p-6 rounded-2xl shadow-sm border border-gray-100/50 hover:shadow-md transition-all duration-300">
			<div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
				<div className="flex items-center gap-3">
					<div className="p-2.5 bg-gradient-to-br from-green-100 to-green-50 rounded-xl shadow-sm">
						<Package size={20} className="text-green-600" />
					</div>
					<div>
						<h3 className="font-semibold text-gray-900 text-base sm:text-lg">Produtos Mais Vendidos</h3>
						<p className="text-xs sm:text-sm text-gray-500">Distribuição de vendas por produto</p>
					</div>
				</div>
				<button className="flex items-center gap-2 text-xs sm:text-sm text-blue-600 hover:text-blue-700 transition-colors self-start sm:self-auto px-3 py-1.5 rounded-lg hover:bg-blue-50">
					<TrendingUp size={14} />
					<span className="hidden sm:inline">Ver detalhes</span>
					<span className="sm:hidden">Detalhes</span>
				</button>
			</div>

			<div className="w-full transition-all duration-300" style={{ height: chartHeight }}>
				{isLoading ? (
					<LoadingState />
				) : !hasData ? (
					<EmptyState />
				) : (
					<ResponsiveContainer width="100%" height="100%">
						<PieChart>
							<defs>
								{COLORS.map((color, index) => (
									<linearGradient key={index} id={`gradient-${index}`} x1="0" y1="0" x2="1" y2="1">
										<stop offset="0%" stopColor={color} stopOpacity={0.9} />
										<stop offset="100%" stopColor={color} stopOpacity={0.7} />
									</linearGradient>
								))}
							</defs>
							<Pie
								data={processedData}
								cx="50%"
								cy="45%"
								outerRadius={pieRadius}
								innerRadius={isMobile ? 20 : 30}
								dataKey="value"
								nameKey="name"
								label={!isMobile ? ({ percent }) => `${(percent * 100).toFixed(0)}%` : false}
								labelLine={false}
								stroke="white"
								strokeWidth={2}
							>
								{processedData.map((_, index) => (
									<Cell
										key={`cell-${index}`}
										fill={`url(#gradient-${index % COLORS.length})`}
										className="hover:opacity-80 transition-all duration-200 cursor-pointer drop-shadow-sm"
									/>
								))}
							</Pie>
							<Tooltip content={<CustomTooltip />} />
							<Legend content={<CustomLegend />} />
						</PieChart>
					</ResponsiveContainer>
				)}
			</div>
		</div>
	);
});

ProductsChart.displayName = "ProductsChart";
